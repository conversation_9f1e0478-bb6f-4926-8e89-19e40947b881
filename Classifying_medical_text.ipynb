import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from transformers import logging
logging.set_verbosity_error()
import seaborn as sns
sns.set(style="whitegrid", palette='Paired')
%matplotlib inline

medquad_df = pd.read_csv('datasets/medquad.csv')
medquad_df.head()

medquad_df.info()

# Count the number of unique focus areas
medquad_df['focus_area'].nunique()

# Counts of the top 20 most common focus areas
medquad_df['focus_area'].value_counts().head(25)

# Group the top 25 most common focus areas into 5 broader categories (focus groups)
focus_area_map = {
    'Cancers': ['Breast Cancer', 'Prostate Cancer', 'Skin Cancer', 'Colorectal Cancer', 'Lung Cancer', 'Leukemia'],
    'Cardiovascular Diseases': ['Stroke', 'Heart Failure', 'Heart Attack', 'High Blood Cholesterol', 'High Blood Pressure'],
    'Metabolic & Endocrine Disorders': ['Causes of Diabetes', 'Diabetes', 'Diabetic Retinopathy', 'Hemochromatosis', 'Kidney Disease'],
    'Neurological & Cognitive Disorders': ['Alzheimer\'s Disease', 'Parkinson\'s Disease', 'Balance Problems'],
    'Other Age-Related & Immune Disorders': ['Shingles', 'Osteoporosis', 'Age-related Macular Degeneration', 'Psoriasis', 'Gum (Peridontal) Disease', 'Dry Mouth']
}

# Create reverse mapping
condition_to_focus_area = {
    condition: focus_area
    for focus_area, conditions in focus_area_map.items()
    for condition in conditions
}

# Create new column 'focus_group' containing focus group
medquad_df['focus_group'] = medquad_df['focus_area'].map(condition_to_focus_area)

# Verify the number of unique focus groups
n_focus_groups = medquad_df['focus_group'].nunique()
print("Number of unique focus groups:", n_focus_groups)


# Dropping missing values including those without a focus group
print("Shape before dropping:", medquad_df.shape)
medquad_df = medquad_df.dropna()
print("Shape after dropping: ", medquad_df.shape)

# Plot focus group counts
sns.countplot(x='focus_group', data=medquad_df, hue='focus_group')
plt.title('Focus Group Counts')
plt.xticks(rotation=90)
plt.show()

print(medquad_df['focus_group'].value_counts(0))
print(medquad_df['focus_group'].value_counts(1))

# Character lengths
medquad_df['answer_char_length'] = medquad_df['answer'].astype(str).apply(len)
# Word lengths
medquad_df['answer_word_count'] = medquad_df['answer'].astype(str).apply(lambda x: len(x.split()))

# Calculate descriptive statistics
answer_length_stats = medquad_df[['answer_char_length', 'answer_word_count']].describe()
answer_length_stats

# Visualize the distribution of word counts
plt.figure(figsize=(16,4))
plt.subplot(1, 2, 1)
sns.histplot(medquad_df['answer_word_count'], kde=True, color='skyblue')
plt.title('Distribution of Answer Word Counts', fontsize=10)
plt.xlabel('Number of Words', fontsize=10)
plt.ylabel('Frequency', fontsize=10)
plt.axvline(x=answer_length_stats['answer_word_count']['mean'], color='red', linestyle='--', label=f'Mean: {answer_length_stats['answer_word_count']['mean']:.2f}')
plt.legend()
plt.show()

focus_map = {'Neurological & Cognitive Disorders': 0, 'Cancers': 1, 'Cardiovascular Diseases': 2, 'Metabolic & Endocrine Disorders': 3, 'Other Age-Related & Immune Disorders': 4}
medquad_df['focus_group'] = medquad_df['focus_group'].replace(focus_map)

# Return all rows that are duplicates of a previous row
print("Shape before duplicates:", medquad_df.shape)
duplicates = medquad_df[medquad_df.duplicated(subset='answer')]
medquad_df = medquad_df.drop_duplicates(subset='answer')
print("Shaoe after removing duplicates:", medquad_df.shape)

from sklearn.model_selection import train_test_split

train_df, test_df = train_test_split(medquad_df,
                                     test_size=0.2,
                                     random_state=42,
                                     stratify=medquad_df['focus_group'])

print('Training set distribution:', train_df['focus_group'].value_counts(1))
print('Testing set distribution:',test_df['focus_group'].value_counts(1))

train_texts = train_df['answer'].tolist()
train_labels = train_df['focus_group'].tolist()

# Print the first two training texts and labels
print(train_texts[:2])
print(train_labels[:2])

import re

remove_keywords = ['Breast Cancer', 'Prostate Cancer', 'Skin Cancer',
    'Colorectal Cancer', 'Lung Cancer', 'Leukemia', 'Stroke', 'Heart Failure', 'Heart Attack',
    'High Blood Cholesterol', 'High Blood Pressure', 'Causes of Diabetes', 'Diabetes', 'Diabetic Retinopathy',
    'Hemochromatosis', 'Kidney Disease', 'Alzheimer\'s Disease', 'Parkinson\'s Disease', 'Balance Problems',
    'Shingles', 'Osteoporosis', 'Age-related Macular Degeneration',
    'Psoriasis', 'Gum (Periodontal) Disease', 'Dry Mouth']

# Split all multi-word phrases into individual words
# Ex. "High Blood Cholesterol" ==> "High", "Blood", "Cholesterol"
words_to_remove = set()
for keyword in remove_keywords:
    for word in re.findall(r'\b\w+\b', keyword):
        words_to_remove.add(word.lower()) # lowercased for case-insensitive match

# Create regex pattern to match any of the words
pattern = re.compile(r'\b(?:' + '|'.join(map(re.escape, words_to_remove)) + r')\b', flags=re.IGNORECASE)

# Remove individual words from each text
masked_train_texts = [pattern.sub('', text) for text in train_texts]

# Normalize whitespace
masked_train_texts = [re.sub(r'\s+', ' ', text).strip() for text in masked_train_texts]

import re
def tokenize(text):
    tokenized_text = re.findall(r'\b\w+\b', text.lower())
    return tokenized_text

tokenized_train_texts = [tokenize(focus_area_text) for focus_area_text in masked_train_texts]

# Print the first tokenized text sequence
print(tokenized_train_texts[0])

from collections import Counter
combined_training_corpus = []
for text in tokenized_train_texts:
    for token in text:
        combined_training_corpus.append(token)

word_freqs = Counter(combined_training_corpus)


MAX_VOCAB_SIZE = 1000
most_common_words = word_freqs.most_common(MAX_VOCAB_SIZE)
print("Top 10 most common words:", most_common_words[:10])

vocab = {word: idx + 2 for idx, (word, freq) in enumerate(most_common_words)}
vocab['<unk>'] = 0
vocab['<pad>'] = 1

len(vocab)

def encode_text(text, vocab):
    tokenized_text = tokenize(text)
    encoded_text = [vocab.get(word, vocab['<unk>']) for word in tokenized_text]
    return encoded_text

def pad_or_truncate(encoded_text, max_length):
    if len(encoded_text) > max_length:
        return encoded_text[:max_length]
    else:
        return encoded_text + [vocab['<pad>']] * (max_length - len(encoded_text))

MAX_SEQ_LENGTH = 100
padded_train_seqs = [pad_or_truncate(encode_text(seq, vocab), max_length=MAX_SEQ_LENGTH) for seq in masked_train_texts]

import torch
X_train_tensor = torch.tensor(padded_train_seqs)
y_train_tensor = torch.tensor(train_labels, dtype=torch.long)

from torch.utils.data import TensorDataset, DataLoader
batch_size=16
train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

import torch.nn as nn
import torch.optim as optim
torch.manual_seed(42)

class SimpleNNWithEmbedding(nn.Module):
    def __init__(self, vocab_size, embed_size, hidden_size, output_size):
        super(SimpleNNWithEmbedding, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embed_size)
        self.fc1 = nn.Linear(embed_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        x = self.embedding(x)
        x = torch.mean(x, dim=1)  # Average pooling
        x = self.fc1(x)
        x = torch.relu(x)
        x = self.fc2(x)
        return x

vocab_size = len(vocab)
embed_size = 50
hidden_size = 100
output_size = n_focus_groups

nn_classifier = SimpleNNWithEmbedding(vocab_size, embed_size, hidden_size, output_size)
print(nn_classifier)

criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(nn_classifier.parameters(), lr=0.0001)

def train_model(model, train_dataloader, criterion, optimizer, num_epochs=5):
    for epoch in range(num_epochs):
        model.train()
        epoch_loss = 0.0
        for batch_X, batch_y in train_dataloader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()

        avg_loss = epoch_loss / len(train_dataloader)
        if (epoch + 1) % 100 == 0:
            print(f'Epoch {epoch+1}/{num_epochs}, Average CE Loss: {avg_loss:.10f}')

# Training the model
train_model(nn_classifier, train_dataloader, criterion, optimizer, num_epochs=500)

# Convert the testing texts and labels to lists
test_texts = test_df['answer'].tolist()
test_labels = test_df['focus_group'].tolist()

# Encode and pad/truncate the testing sequences
padded_test_seqs = [pad_or_truncate(encode_text(seq, vocab), MAX_SEQ_LENGTH) for seq in test_texts]

# Convert testing sequences to tensors
X_tensor_test = torch.tensor(padded_test_seqs)
y_tensor_test = torch.tensor(test_labels, dtype=torch.long)

# Batch the testing set
from torch.utils.data import DataLoader, TensorDataset
test_dataset = TensorDataset(X_tensor_test, y_tensor_test)
test_dataloader = DataLoader(test_dataset, batch_size=8, shuffle=False)

import torch.nn.functional as F

def get_predictions_and_probabilities(model, test_loader):
    model.eval()

    all_probs = []
    all_labels = []

    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            outputs = model(batch_X)
            probs = F.softmax(outputs, dim=1)
            all_probs.extend(probs.cpu().numpy())
            predicted_labels = torch.argmax(outputs, dim=1)
            all_labels.extend(predicted_labels.cpu().numpy())

    return all_probs, all_labels

# Generate predictions
nn_pred_probs, nn_pred_labels = get_predictions_and_probabilities(nn_classifier, test_dataloader)

from sklearn.metrics import confusion_matrix, classification_report

focus_group_names = ["Neurological & Cognitive Disorders", "Cancers", "Cardiovascular Diseases", 
                     "Metabolic & Endocrine Disorders", "Other Age-Related & Immune Disorders"]

confusion_matrix = confusion_matrix(test_labels, nn_pred_labels)
report = classification_report(test_labels, nn_pred_labels, target_names=focus_group_names)

print("Confusion Matrix:\n", confusion_matrix)
print("\nClassification Report:\n", report)

from transformers import Autotokenizer, AutoModelForSequenceClassification
import torch

# Loading BiomedBERT model and tokenizer
model_name = "microsoft/BiomedNLP-BiomedBERT-base-uncased-abstract-fulltext"
bert_tokenizer = AutoTokenizer.from_pretrained(model_name)
bert_model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=n_focus_groups)

