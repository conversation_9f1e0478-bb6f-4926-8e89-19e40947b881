import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from transformers import logging
logging.set_verbosity_error()
import seaborn as sns
sns.set(style="whitegrid", palette='Paired')
%matplotlib inline

medquad_df = pd.read_csv('datasets/medquad.csv')
medquad_df.head()

medquad_df.info()

# Count the number of unique focus areas
medquad_df['focus_area'].nunique()

# Counts of the top 20 most common focus areas
medquad_df['focus_area'].value_counts().head(25)

# Group the top 25 most common focus areas into 5 broader categories (focus groups)
focus_area_map = {
    'Cancers': ['Breast Cancer', 'Prostate Cancer', 'Skin Cancer', 'Colorectal Cancer', 'Lung Cancer', 'Leukemia'],
    'Cardiovascular Diseases': ['Stroke', 'Heart Failure', 'Heart Attack', 'High Blood Cholesterol', 'High Blood Pressure'],
    'Metabolic & Endocrine Disorders': ['Causes of Diabetes', 'Diabetes', 'Diabetic Retinopathy', 'Hemochromatosis', 'Kidney Disease'],
    'Neurological & Cognitive Disorders': ['Alzheimer\'s Disease', 'Parkinson\'s Disease', 'Balance Problems'],
    'Other Age-Related & Immune Disorders': ['Shingles', 'Osteoporosis', 'Age-related Macular Degeneration', 'Psoriasis', 'Gum (Peridontal) Disease', 'Dry Mouth']
}

# Create reverse mapping
condition_to_focus_area = {
    condition: focus_area
    for focus_area, conditions in focus_area_map.items()
    for condition in conditions
}

# Create new column 'focus_group' containing focus group
medquad_df['focus_group'] = medquad_df['focus_area'].map(condition_to_focus_area)

# Verify the number of unique focus groups
n_focus_groups = medquad_df['focus_group'].nunique()
print("Number of unique focus groups:", n_focus_groups)


# Dropping missing values including those without a focus group
print("Shape before dropping:", medquad_df.shape)
medquad_df = medquad_df.dropna()
print("Shape after dropping: ", medquad_df.shape)

# Plot focus group counts
sns.countplot(x='focus_group', data=medquad_df, hue='focus_group')
plt.title('Focus Group Counts')
plt.xticks(rotation=90)
plt.show()

print(medquad_df['focus_group'].value_counts(0))
print(medquad_df['focus_group'].value_counts(1))

# Character lengths
medquad_df['answer_char_length'] = medquad_df['answer'].astype(str).apply(len)
# Word lengths
medquad_df['answer_word_count'] = medquad_df['answer'].astype(str).apply(lambda x: len(x.split()))

# Calculate descriptive statistics
answer_length_stats = medquad_df[['answer_char_length', 'answer_word_count']].describe()
answer_length_stats

# Visualize the distribution of word counts
plt.figure(figsize=(16,4))
plt.subplot(1, 2, 1)
sns.histplot(medquad_df['answer_word_count'], kde=True, color='skyblue')
plt.title('Distribution of Answer Word Counts', fontsize=10)
plt.xlabel('Number of Words', fontsize=10)
plt.ylabel('Frequency', fontsize=10)
plt.axvline(x=answer_length_stats['answer_word_count']['mean'], color='red', linestyle='--', label=f'Mean: {answer_length_stats['answer_word_count']['mean']:.2f}')
plt.legend()
plt.show()

focus_map = {'Neurological & Cognitive Disorders': 0, 'Cancers': 1, 'Cardiovascular Diseases': 2, 'Metabolic & Endocrine Disorders': 3, 'Other Age-Rleated & Immune Disorders': 4}
medquad_df['focus_group'] = medquad_df['focus_group'].replace(focus_map)

# Return all rows that are duplicates of a previous row
print("Shape before duplicates:", medquad_df.shape)
duplicates = medquad_df[medquad_df.duplicated(subset='answer')]
medquad_df = medquad_df.drop_duplicates(subset='answer')
print("Shaoe after removing duplicates:", medquad_df.shape)

from sklearn.model_selection import train_test_split

train_df, test_df = train_test_split(medquad_df,
                                     test_size=0.2,
                                     random_state=42,
                                     stratify=medquad_df['focus_group'])

print('Training set distribution:', train_df['focus_group'].value_counts(1))
print('Testing set distribution:',test_df['focus_group'].value_counts(1))

train_texts = train_df['answer'].tolist()
train_labels = train_df['focus_group'].tolist()

# Print the first two training texts and labels
print(train_texts[:2])
print(train_labels[:2])