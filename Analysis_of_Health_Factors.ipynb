import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
%matplotlib inline

diabetes_data = pd.read_csv("datasets\diabetes_BRFSS2015.csv")
diabetes_data.head()

diabetes_data.info()

# Plot Diabetes Status
sns.countplot(x='Diabetes', data=diabetes_data, hue='Diabetes')
plt.title('Diabetes Status')
plt.show()

print(diabetes_data['Diabetes'].value_counts(0))
print(diabetes_data['Diabetes'].value_counts(1))

# Removing individuals with prediabetes
diabetes_data = diabetes_data[diabetes_data['Diabetes'] != 'Prediabetes']

# Binary encoding 'Diabetes' column - {'No Diabetes': 0, 'Diabetes': 1}
diabetes_data['Diabetes'] = diabetes_data['Diabetes'].map({'No Diabetes': 0, 'Diabetes': 1})

# Plotting diabetes status by sex
sns.countplot(x='Sex', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by Sex')
plt.show()

# Calculate percentage of diabetes cases by sex
diabetes_by_sex = diabetes_data.groupby('Sex')['Diabetes'].mean()
print(diabetes_by_sex)

# Set the figure size
plt.figure(figsize=(10, 6))

# Plot Diabetes Status by Age Group
sns.countplot(x='Age', hue='Diabetes', data=diabetes_data, order=['18-24', '25-29', '30-34', '35-39', '40-44', '45-49', '50-54', '55-59', '60-64', '65-69', '70-74', '75-79', '80+'])
plt.title('Diabetes Status by Age Group')
plt.xticks(rotation=45)
plt.show()

# Calculate percentage of diabetes cases by age group
diabetes_by_age = diabetes_data.groupby('Age')['Diabetes'].mean()
print(diabetes_by_age)

# Plot Diabetes Status by High Blood Pressure
sns.countplot(x='HighBP', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by High Blood Pressure')
plt.show()

# Calculate percentage of diabetes cases by high blood pressure
diabetes_by_blood_pressure = diabetes_data.groupby('HighBP')['Diabetes'].mean()
print(diabetes_by_blood_pressure)

# Plot diabetes status by healthcare coverage
sns.countplot(x='AnyHealthcare', hue='Diabetes', data=diabetes_data)
plt.title('Diabetes Status by Healthcare Coverage')
plt.show()

# Calculate percentage of diabetes cases by healthcare coverage
print(diabetes_data['AnyHealthcare'].value_counts(1))
diabetes_data.groupby('AnyHealthcare')['Diabetes'].mean()

import matplotlib.pyplot as plt
import seaborn as sns

binary_features = ['HighChol', 'CholCheck', 'Smoker', 'Stroke', 'HeartDiseaseorAttack', 
                   'PhysActivity', 'Fruits', 'Veggies', 'HvyAlcoholConsump']

# Calculate percentages of diabetes by binary factor
diabetes_rates = {}
for feature in binary_features:
    rate = diabetes_data.groupby(feature)['Diabetes'].mean()[1] 
    diabetes_rates[feature] = rate

rate_df = pd.DataFrame(list(diabetes_rates.items()), columns=['Feature', 'DiabetesRate'])

# Plot Diabetes Rate by Binary Feature
plt.figure(figsize=(10,3))
sns.barplot(x='Feature', y='DiabetesRate', data=rate_df, palette='Blues')
plt.title('Diabetes Rate by Binary Features')
plt.xticks(rotation=45)
plt.show()

# Print table of percentages
diabetes_percentage_table = pd.DataFrame.from_dict(diabetes_rates, orient='index', columns=['Diabetes Percentage'])
diabetes_percentage_table = diabetes_percentage_table.sort_values(by='Diabetes Percentage', ascending=False)
diabetes_percentage_table

# Plot BMI Distribution by Diabetes
fig, axes = plt.subplots(1, 2, figsize=(10, 5))

# Boxplot
sns.boxplot(x='Diabetes', y='BMI', data=diabetes_data, ax=axes[0])
axes[0].set_title('BMI Distribution by Diabetes (Boxplot)')
axes[0].set_xlabel('Diabetes')
axes[0].set_ylabel('BMI')

# KDE Plot
sns.kdeplot(data=diabetes_data, x='BMI', hue='Diabetes', fill=True, common_norm=False, ax=axes[1])
axes[1].set_title('BMI Distribution by Diabetes (KDE Plot)')
axes[1].set_xlabel('BMI')
axes[1].set_ylabel('Density')

plt.tight_layout()
plt.show()

# Print summary statistics by diabetes status
print("BMI Summary Statistics by Diabetes Status:\n", diabetes_data.groupby('Diabetes')['BMI'].describe())

# Balance the dataset 50/50
diabetics = diabetes_data[diabetes_data['Diabetes'] == 1] # Dataset of diabetic individuals
nondiabetics = diabetes_data[diabetes_data['Diabetes'] == 0] # Dataset of nondiabetic individuals

# Undersample nondiabetic individuals + keep all diabetic individuals
nondiabetics_sampled = nondiabetics.sample(n=len(diabetics), random_state=42) 
diabetes_data_balanced = pd.concat([diabetics, nondiabetics_sampled])

# Shuffle the combined dataset + verify balance
diabetes_data_balanced = diabetes_data_balanced.sample(frac=1, random_state=42).reset_index(drop=True)
print(diabetes_data_balanced['Diabetes'].value_counts())

# Sex
diabetes_data_balanced['Sex'] = diabetes_data_balanced['Sex'].replace({'Male': 1, 'Female': 0})

# Age
age_mapping = {'18-24': 1, '25-29': 2, '30-34': 3, '35-39': 4, '40-44': 5, '45-49': 6, '50-54': 7, '55-59': 8, '60-64': 9, '65-69': 10, '70-74': 11, '75-79': 12, '80+': 13}
diabetes_data_balanced['Age'] = diabetes_data_balanced['Age'].map(age_mapping)

# Education
education_mapping = {"Never attended school or only kindergarten":1,
                     "Grades 1-8 (Elementary)":2,
                     "Grades 9-11 (Some high school)":3,
                     "Grade 12 or GED (High school graduate)":4,
                     "College 1-3 years (Some college/technical school)":5,
                     "College 4+ years (College graduate)":6}

diabetes_data_balanced['Education'] = diabetes_data_balanced['Education'].map(education_mapping)

# Income
income_mapping = {"Less than $10,000":1,
                  "Less than $15,000":2,
                  "Less than $20,000":3,
                  "Less than $25,000":4,
                  "Less than $35,000":5,
                  "Less than $50,000":6,
                  "Less than $75,000":7,
                  "$75,000 or more":8}

diabetes_data_balanced['Income'] = diabetes_data_balanced['Income'].map(income_mapping)

# Binary features
binary_columns = ['HighBP', 'HighChol', 'CholCheck', 'Smoker', 
                  'Stroke', 'HeartDiseaseorAttack', 'PhysActivity', 'Fruits', 
                  'Veggies', 'HvyAlcoholConsump', 'AnyHealthcare', 
                  'NoDocbcCost', 'DiffWalk']

diabetes_data_balanced[binary_columns] = diabetes_data_balanced[binary_columns].replace({'Yes': 1, 'No': 0})

diabetes_data_balanced['NoDocbcCost'].value_counts()

# Drop unimportant features
drop_columns = ['CholCheck', 'NoDocbcCost']
diabetes_data_balanced = diabetes_data_balanced.drop(columns=drop_columns, axis=1)

# Preview before training
diabetes_data_balanced.head()

# Select training features
remove_cols = ['Diabetes']
train_features = [x for x in diabetes_data_balanced.columns if x not in remove_cols]
print("Number of training features:", len(train_features))

# Convert into tensors
import torch

X = torch.tensor(diabetes_data_balanced[train_features].values, dtype=torch.float)
y = torch.tensor(diabetes_data_balanced['Diabetes'].values, dtype=torch.float32).view(-1, 1)

from sklearn.model_selection import train_test_split

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

print('Training Shape:', X_train.shape)
print('Testing Shape', X_test.shape)

import torch.nn as nn
import torch.optim as optim
torch.manual_seed(42)

# NN architecture
binary_model = nn.Sequential(
    nn.Linear(19, 48),
    nn.ReLU(),
    nn.Linear(48, 16),
    nn.ReLU(),
    nn.Linear(16, 12),
    nn.GELU(),
    nn.Linear(12, 1),
    nn.Sigmoid()
)

# Initialize loss function + optimizer
loss = nn.BCELoss()
optimizer = optim.Adam(binary_model.parameters(), lr=0.001)

# Training loop
from sklearn.metrics import accuracy_score
import torch.nn.functional as F

num_epochs = 2000
for epoch in range(num_epochs):
    binary_model.train()
    predictions = binary_model(X_train)
    BCELoss = loss(predictions, y_train)
    BCELoss.backward()
    optimizer.step()
    optimizer.zero_grad()

    if (epoch + 1) % 500 == 0:
        predicted_labels = (predictions >= 0.5).int()
        accuracy = accuracy_score(y_train, predicted_labels)
        print(f'Epoch [{epoch+1}/{num_epochs}], BCELoss: {BCELoss.item():.4f}, Accuracy: {accuracy:.4f}')

# Set the model to evaluation mode
binary_model.eval()

# Generate test set predictions
with torch.no_grad():
    test_predictions = binary_model(X_test)
    test_predicted_labels = (test_predictions >= 0.5).int()

# Classification report
from sklearn.metrics import classification_report, accuracy_score
test_accuracy = accuracy_score(y_test, test_predicted_labels)
print(f'Accuracy: {test_accuracy:.4f}')

report = classification_report(y_test, test_predicted_labels)
print("Classification Report:\n", report)

# Select training features
remove_cols = ['Age']
train_features = [x for x in diabetes_data_balanced.columns if x not in remove_cols]

print('Number of training features:', len(train_features))


# Convert into tensors
import torch

X = torch.tensor(diabetes_data_balanced[train_features].values, dtype=torch.float)
y = torch.tensor(diabetes_data_balanced['Age'].values, dtype=torch.float32).view(-1, 1) 

# Split the data into training and testing sets
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, train_size=0.8, test_size=0.2, random_state=42)

print("Training Shape:", X_train.shape)
print("Testing Shape:", X_test.shape)

from sklearn.linear_model import LinearRegression
linear_model = LinearRegression()
linear_model.fit(X_train, y_train)

from sklearn.metrics import mean_squared_error
linear_test_prediction = linear_model.predict(X_test)
linear_test_mse = mean_squared_error(y_test, linear_test_prediction)
print(f"Linear Regression Test MSE: {linear_test_mse:.4f}")

import torch.nn as nn
import torch.optim as optim
torch.manual_seed(42)

# NN architecture
regression_model = nn.Sequential(
    nn.Linear(19, 48),
    nn.ReLU(),
    nn.Linear(48, 24),
    nn.ReLU(),
    nn.Linear(24, 8),
    nn.ReLU(),
    nn.Linear(8, 1)
)

# Initialize loss function + optimizer
loss = nn.MSELoss()
optimizer = optim.Adam(regression_model.parameters(), lr=0.001)

# Training Loop
num_epochs = 2000
for epoch in range(num_epochs):
    regression_model.train()
    outputs = regression_model(X_train)
    mse_loss = loss(outputs, y_train)
    mse_loss.backward()
    optimizer.step()
    optimizer.zero_grad()

    if (epoch + 1) % 500 == 0:
        print(f'Epoch [{epoch+1}/{num_epochs}], MSE Loss: {mse_loss.item():.4f}')

# Set the model to evaluation mode
regression_model.eval()

# Generate test set predictions + test set MSE
with torch.no_grad():
    test_predictions = regression_model(X_test)
    nn_test_mse = loss(test_predictions, y_test)

print('Linear Regression - Test Set MSE:', linear_test_mse.item())
print('Neural Network - Test Set MSE:', nn_test_mse.item())